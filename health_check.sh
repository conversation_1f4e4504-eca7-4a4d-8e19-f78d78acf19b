#!/bin/bash

# Pump.fun Bot Health Check Script
# This script monitors the health of the pump-bot service and sends alerts if issues are detected

set -e

# Configuration
SERVICE_NAME="pump-bot"
LOG_FILE="/var/log/pump-bot.log"
ALERT_LOG="/var/log/pump-bot-health.log"
CHECK_WINDOW_MINUTES=5
MAX_ERROR_COUNT=3

# Alert configuration (set these environment variables or modify here)
WEBHOOK_URL="${HEALTH_CHECK_WEBHOOK_URL:-}"
EMAIL_TO="${HEALTH_CHECK_EMAIL:-}"
SLACK_WEBHOOK="${HEALTH_CHECK_SLACK_WEBHOOK:-}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Function to log with timestamp
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$ALERT_LOG"
}

# Function to send alert
send_alert() {
    local subject=$1
    local message=$2
    local severity=$3
    
    log_message "ALERT" "$subject: $message"
    
    # Send webhook alert if configured
    if [ -n "$WEBHOOK_URL" ]; then
        curl -s -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"subject\":\"$subject\",\"message\":\"$message\",\"severity\":\"$severity\",\"service\":\"$SERVICE_NAME\",\"timestamp\":\"$(date -Iseconds)\"}" \
            >/dev/null 2>&1 || log_message "ERROR" "Failed to send webhook alert"
    fi
    
    # Send Slack alert if configured
    if [ -n "$SLACK_WEBHOOK" ]; then
        local emoji="⚠️"
        [ "$severity" = "critical" ] && emoji="🚨"
        [ "$severity" = "warning" ] && emoji="⚠️"
        [ "$severity" = "info" ] && emoji="ℹ️"
        
        curl -s -X POST "$SLACK_WEBHOOK" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"$emoji *$subject*\n$message\n_Service: $SERVICE_NAME | $(date)_\"}" \
            >/dev/null 2>&1 || log_message "ERROR" "Failed to send Slack alert"
    fi
    
    # Send email if configured
    if [ -n "$EMAIL_TO" ] && command -v mail >/dev/null 2>&1; then
        echo "$message" | mail -s "$subject - $SERVICE_NAME" "$EMAIL_TO" 2>/dev/null || \
            log_message "ERROR" "Failed to send email alert"
    fi
}

# Function to check if service is running
check_service_status() {
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        return 0
    else
        return 1
    fi
}

# Function to check recent log activity
check_log_activity() {
    if [ ! -f "$LOG_FILE" ]; then
        return 1
    fi
    
    # Check for any log entries in the last 5 minutes
    local recent_logs=$(find "$LOG_FILE" -mmin -$CHECK_WINDOW_MINUTES -type f 2>/dev/null)
    if [ -z "$recent_logs" ]; then
        # File exists but no recent modifications, check if there are recent entries
        local cutoff_time=$(date -d "$CHECK_WINDOW_MINUTES minutes ago" '+%Y-%m-%d %H:%M:%S')
        local recent_entries=$(awk -v cutoff="$cutoff_time" '$0 >= cutoff' "$LOG_FILE" 2>/dev/null | wc -l)
        
        if [ "$recent_entries" -gt 0 ]; then
            return 0
        else
            return 1
        fi
    fi
    return 0
}

# Function to check for error patterns
check_error_patterns() {
    if [ ! -f "$LOG_FILE" ]; then
        return 0
    fi
    
    # Look for error patterns in the last 5 minutes
    local cutoff_time=$(date -d "$CHECK_WINDOW_MINUTES minutes ago" '+%Y-%m-%d %H:%M:%S')
    local error_count=$(awk -v cutoff="$cutoff_time" '$0 >= cutoff && /ERROR|FATAL|Failed|failed|Error|error/ {count++} END {print count+0}' "$LOG_FILE" 2>/dev/null)
    
    if [ "$error_count" -gt "$MAX_ERROR_COUNT" ]; then
        return 1
    fi
    return 0
}

# Function to check connection to Jito Shredstream
check_shredstream_connection() {
    if [ ! -f "$LOG_FILE" ]; then
        return 1
    fi
    
    # Check for successful connection messages in recent logs
    local cutoff_time=$(date -d "$CHECK_WINDOW_MINUTES minutes ago" '+%Y-%m-%d %H:%M:%S')
    local connection_success=$(awk -v cutoff="$cutoff_time" '$0 >= cutoff && /Successfully connected to Jito Shredstream/ {count++} END {print count+0}' "$LOG_FILE" 2>/dev/null)
    local connection_failure=$(awk -v cutoff="$cutoff_time" '$0 >= cutoff && /Failed to connect to Jito Shredstream/ {count++} END {print count+0}' "$LOG_FILE" 2>/dev/null)
    
    if [ "$connection_failure" -gt 0 ] && [ "$connection_success" -eq 0 ]; then
        return 1
    fi
    return 0
}

# Main health check function
main() {
    local issues=0
    local warnings=0
    
    log_message "INFO" "Starting health check for $SERVICE_NAME"
    
    # Check 1: Service status
    if ! check_service_status; then
        send_alert "Service Down" "The $SERVICE_NAME service is not running" "critical"
        issues=$((issues + 1))
    else
        log_message "INFO" "Service status: OK"
    fi
    
    # Check 2: Recent log activity
    if ! check_log_activity; then
        send_alert "No Recent Activity" "No log activity detected in the last $CHECK_WINDOW_MINUTES minutes" "warning"
        warnings=$((warnings + 1))
    else
        log_message "INFO" "Log activity: OK"
    fi
    
    # Check 3: Error patterns
    if ! check_error_patterns; then
        send_alert "High Error Rate" "More than $MAX_ERROR_COUNT errors detected in the last $CHECK_WINDOW_MINUTES minutes" "warning"
        warnings=$((warnings + 1))
    else
        log_message "INFO" "Error rate: OK"
    fi
    
    # Check 4: Shredstream connection
    if ! check_shredstream_connection; then
        send_alert "Connection Issues" "Jito Shredstream connection problems detected" "critical"
        issues=$((issues + 1))
    else
        log_message "INFO" "Shredstream connection: OK"
    fi
    
    # Summary
    if [ "$issues" -eq 0 ] && [ "$warnings" -eq 0 ]; then
        log_message "INFO" "Health check completed: All systems healthy"
        exit 0
    elif [ "$issues" -eq 0 ]; then
        log_message "WARN" "Health check completed: $warnings warnings detected"
        exit 1
    else
        log_message "ERROR" "Health check completed: $issues critical issues, $warnings warnings"
        exit 2
    fi
}

# Create alert log directory if it doesn't exist
mkdir -p "$(dirname "$ALERT_LOG")"

# Run main function
main "$@"
