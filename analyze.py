import os
import re
import csv
import asyncio
import httpx
from typing import List, Dict, Any

async def get_token_supply(token_address: str) -> Any:
    headers = {
        "User-Agent": "rust-client",
        "x-api-key": os.getenv("SOL_TRACK_API_KEY", "73155f80-4998-4182-90a0-e53b057ff94d"),
    }

    url = f"https://data.solanatracker.io/tokens/{token_address}"
    async with httpx.AsyncClient() as client:
        resp = await client.get(url, headers=headers)
        resp.raise_for_status()
        data = resp.json()

        pools = data.get("pools", [])
        if not pools:
            return 0

        return pools[0].get("tokenSupply", 0)

async def get_token_trade_info(mint_key: str) -> List[Dict[str, Any]]:
    headers = {
        "User-Agent": "Mozilla/5.0",
        "x-api-key": os.getenv("SOL_TRACK_API_KEY", "73155f80-4998-4182-90a0-e53b057ff94d"),
    }

    url = (
        f"https://data.solanatracker.io/trades/{mint_key}"
        f"?tokens={mint_key}&token={mint_key}&showMeta=0&cursor=&sortDirection=DESC"
    )

    async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=headers)
        response.raise_for_status()
        data = response.json()
        return data.get("trades", [])


async def main():
    # Load buy log lines
    with open("logs/bought_tokens.txt", "r", encoding="utf-8") as file:
        lines = file.readlines()

    # Load sell log lines
    with open("logs/sell_tokens.txt", "r", encoding="utf-8") as file:
        sell_lines = file.readlines()

    mint_keys = []
    timestamps = []
    sell_times5secs = []
    sell_times4secs = []
    max_prices_usd = []

    # Extract mint keys and timestamps
    for line in lines:
        time_match = re.search(r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}:\d{3}", line)
        mint_match = re.search(r"mint_key: ([\w\d]+)", line)
        if time_match and mint_match:
            timestamps.append(time_match.group())
            mint_keys.append(mint_match.group(1))

    for mint in mint_keys:
        found1 = next((sell for sell in sell_lines if mint in sell and "Sell Alert: 5 secs Stop Loss History" in sell), None)
        found2 = next((sell for sell in sell_lines if mint in sell and "Sell Alert: 4 secs Stop Loss History" in sell), None)

        if found1:
            sell_time_match = re.search(r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}:\d{3}", found1)
            sell_times5secs.append(sell_time_match.group() if sell_time_match else "")
        else:
            sell_times5secs.append("")

        if found2:
            sell_time_match = re.search(r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}:\d{3}", found2)
            sell_times4secs.append(sell_time_match.group() if sell_time_match else "")
        else:
            sell_times4secs.append("")

        # Call API and extract max priceUsd
        try:
            trades = await get_token_trade_info(mint)
            max_price = max((trade.get("priceUsd", 0) for trade in trades if "priceUsd" in trade), default=0)
            supply = await get_token_supply(mint)
            max_prices_usd.append(max_price * supply)
            
        except Exception as e:
            print(f"Error fetching trade info for {mint}: {e}")
            max_prices_usd.append("")

    # Write to CSV
    with open("output.csv", "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(["Buy Time", "Mint Key", "Sell Time (5 secs)", "Sell Time (4 secs)", "Max priceUsd"])
        for buy_time, mint, sell5, sell4, max_price in zip(timestamps, mint_keys, sell_times5secs, sell_times4secs, max_prices_usd):
            writer.writerow([buy_time, mint, sell5, sell4, max_price])

    # write to txt file
    with open("output.txt", "w", encoding="utf-8") as file:
        for buy_time, mint, sell5, sell4, max_price in zip(timestamps, mint_keys, sell_times5secs, sell_times4secs, max_prices_usd):
            file.write(f"Buy Time: {buy_time} | Mint Key: {mint} | Sell Time (5 secs): {sell5} | Sell Time (4 secs): {sell4} | Max priceUsd: {max_price}\n")

    print("✅ Exported to output.csv")


# Run it
if __name__ == "__main__":
    asyncio.run(main())