#!/bin/env bash
set -euo pipefail

# setup_service.sh
# Installs and starts the Pump.fun monitoring bot as a systemd service.

SERVICE_NAME="pump-bot"
SERVICE_FILE="pump-bot.service"
PROJECT_DIR="/home/<USER>/work/jito"
USER="ubuntu"
LOG_DIR="/var/log/pump-bot"
ENV_FILE="${PROJECT_DIR}/.env"
ENV_EXAMPLE_FILE="${PROJECT_DIR}/.env.example"

echo "🔧 Setting up ${SERVICE_NAME} service..."

# 1. Build the release binary as the ubuntu user
echo "📦 Building release binary..."
runuser -l "${USER}" -c "cd ${PROJECT_DIR} && cargo build -p pump-tester --release"

# 2. Copy the systemd service unit
echo "📄 Installing systemd unit..."
if [[ -f "${PROJECT_DIR}/${SERVICE_FILE}" ]]; then
  cp "${PROJECT_DIR}/${SERVICE_FILE}" "/etc/systemd/system/${SERVICE_NAME}.service"
  chmod 644 "/etc/systemd/system/${SERVICE_NAME}.service"
else
  echo "❌ Service file not found at ${PROJECT_DIR}/${SERVICE_FILE}"
  exit 1
fi

# 3. Set up environment file
echo "🌐 Configuring environment file..."
if [[ -f "${ENV_EXAMPLE_FILE}" ]]; then
  if [[ ! -f "${ENV_FILE}" ]]; then
    cp "${ENV_EXAMPLE_FILE}" "${ENV_FILE}"
    chown "${USER}":"${USER}" "${ENV_FILE}"
    chmod 600 "${ENV_FILE}"
    echo "⚙️ .env created from .env.example — please edit values as needed."
  else
    echo "⚙️ .env already exists, skipping."
  fi
else
  echo "⚠️ .env.example not found; ensure environment variables are set via another method."
fi

# 4. Prepare log directory
echo "🗄️ Setting up log directory..."
mkdir -p "${LOG_DIR}"
chown "${USER}":"${USER}" "${LOG_DIR}"
touch "${LOG_DIR}/${SERVICE_NAME}.log"
chown "${USER}":"${USER}" "${LOG_DIR}/${SERVICE_NAME}.log"
chmod 644 "${LOG_DIR}/${SERVICE_NAME}.log"

# 5. Reload systemd and enable service
echo "🔄 Reloading systemd daemon..."
systemctl daemon-reload

echo "✅ Enabling ${SERVICE_NAME} service..."
systemctl enable "${SERVICE_NAME}"

echo "▶️ Starting ${SERVICE_NAME} service..."
systemctl start "${SERVICE_NAME}"

# 6. Verify status
echo "🔎 Verifying service status..."
systemctl status "${SERVICE_NAME}" --no-pager

echo "🎉 Setup complete! Follow logs with:"
echo "   sudo journalctl -u ${SERVICE_NAME} -f"
echo "   tail -f ${LOG_DIR}/${SERVICE_NAME}.log"

exit 0
