#!/bin/bash

# Pump.fun Bot Test Runner
# This script runs all tests for the pump-bot package with proper CI/CD integration

set -e  # Exit on any error

echo "🧪 Running Pump.fun Bot Tests..."
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Check if we're in the correct directory
if [ ! -f "Cargo.toml" ]; then
    print_status $RED "❌ Error: Must be run from the jito project root directory"
    exit 1
fi

# Check if pump-bot package exists
if [ ! -d "pump-bot" ]; then
    print_status $RED "❌ Error: pump-bot package not found"
    exit 1
fi

print_status $YELLOW "📋 Test Configuration:"
echo "  - Package: pump-bot"
echo "  - Output: verbose (--nocapture)"
echo "  - Working Directory: $(pwd)"
echo ""

# Run the tests
print_status $YELLOW "🚀 Executing tests..."
echo ""

# Run tests with verbose output and capture exit code
if cargo test --package pump-bot -- --nocapture; then
    print_status $GREEN "✅ All tests passed successfully!"
    TEST_RESULT=0
else
    print_status $RED "❌ Some tests failed!"
    TEST_RESULT=1
fi

echo ""
print_status $YELLOW "📊 Test Summary:"

# Get test count (this is a simple approach, could be enhanced)
TEST_COUNT=$(cargo test --package pump-bot --quiet 2>/dev/null | grep -c "test result:" || echo "Unknown")
echo "  - Tests executed: $TEST_COUNT"

# Check for test coverage if cargo-tarpaulin is available
if command -v cargo-tarpaulin &> /dev/null; then
    print_status $YELLOW "📈 Generating test coverage..."
    cargo tarpaulin --package pump-bot --out Stdout --timeout 120 || true
else
    print_status $YELLOW "💡 Tip: Install cargo-tarpaulin for test coverage: cargo install cargo-tarpaulin"
fi

echo ""
if [ $TEST_RESULT -eq 0 ]; then
    print_status $GREEN "🎉 Test run completed successfully!"
else
    print_status $RED "💥 Test run failed!"
fi

echo "=================================="

# Exit with the test result code for CI/CD integration
exit $TEST_RESULT
