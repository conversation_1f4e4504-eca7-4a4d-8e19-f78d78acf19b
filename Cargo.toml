[workspace]
members = [
    "examples",
    "jito_protos",
    "proxy",
    "pump-bot",
    "pump-tester"
]
resolver = "2"

[workspace.package]
version = "0.2.5"
description = "Fast path to receive shreds from Jito, forwarding to local consumers. See https://jito-labs.gitbook.io/mev/searcher-services/shredstream for details."
authors = ["Jito Team <<EMAIL>>"]
homepage = "https://jito.wtf/"
edition = "2021"

[profile.release]
# thin has minimal overhead vs none (default): https://blog.llvm.org/2016/06/thinlto-scalable-and-incremental-lto.html
lto = "thin"

[workspace.dependencies]
arc-swap = "1.6"
clap = { version = "4", features = ["derive", "env"] }
crossbeam-channel = "0.5.8"
dashmap = "5"
env_logger = "0.11"
hostname = "0.4.0"
itertools = "0.13.0"
jito-protos = { path = "jito_protos" }
log = "0.4"
prost = "0.12"
prost-types = "0.12"
rand = "0.8"
borsh = "1.5.3"
bincode = "1.3.3"
reqwest = { version = "0.11", features = ["blocking", "json"] }
serde_json = "1"
signal-hook = "0.3"
solana-client = "=2.2.1"
solana-metrics = "=2.2.1"
solana-net-utils = "=2.2.1"
solana-perf = "=2.2.1"
solana-sdk = "=2.2.1"
solana-transaction-status = "=2.2.1"
solana-streamer = "=2.2.1"
solana-ledger = { git = "https://github.com/jito-foundation/jito-solana.git", branch = "eric/v2.2-merkle-recovery", package = "solana-ledger" }
solana-entry = "=2.2.1"
thiserror = "1"
tokio = "1"
tonic = { version = "0.10", features = ["tls", "tls-roots", "tls-webpki-roots"] }
tonic-build = "0.10"

solana-trader-client-rust = "0.1.6"