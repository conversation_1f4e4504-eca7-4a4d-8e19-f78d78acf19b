use jito_protos::shredstream::{
    shredstream_proxy_client::ShredstreamProxyClient, SubscribeEntriesRequest,
};
use solana_sdk::{pubkey::Pubkey, signature::Signature};
use serde::{Deserialize, Serialize};
use std::str::FromStr;
use bincode::Options;

#[derive(Debug, Serialize, Deserialize)]
struct MintParams {
    name: String,
    symbol: String,
    uri: String,
    creator: [u8; 32], // 32-byte array for the public key
}

#[tokio::main]
async fn main() -> Result<(), std::io::Error> {
    let mint_params = MintParams {
        name: "TokenName".to_string(),
        symbol: "TKN".to_string(),
        uri: "https://example.com".to_string(),
        creator: [0; 32],
    };

    // Configure bincode to use fixed-length encoding (4-byte lengths)
    let config = bincode::DefaultOptions::new()
        .with_fixint_encoding() // Use fixed integer encoding
        .with_limit(u32::MAX as u64); // Enforce 4-byte length prefixes

    // Serialize the data
    let encoded_data = config.serialize(&mint_params).expect("Failed to encode");
    println!("Encoded Data: {:?}", encoded_data);
    // Deserialize the data
    match config.deserialize::<MintParams>(&encoded_data) {
        Ok(decoded_args) => {
            println!("Decoded MintParams: {:?}", decoded_args);
        }
        Err(e) => {
            println!("Failed to decode Args: {e}");
        }
    }
    // return
    let mut client = ShredstreamProxyClient::connect("http://127.0.0.1:9900")
        .await
        .unwrap();
    let mut stream = client
        .subscribe_entries(SubscribeEntriesRequest {})
        .await
        .unwrap()
        .into_inner();

    let pump_mint_authority = "TSLvdd1pWpHVjahSpsvCXUbgwsL3JAcvokwaKt1eokM";
    let pump_manager = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";

    while let Some(slot_entry) = stream.message().await.unwrap() {
        let entries =
            match bincode::deserialize::<Vec<solana_entry::entry::Entry>>(&slot_entry.entries) {
                Ok(e) => e,
                Err(e) => {
                    println!("Deserialization failed with err: {e}");
                    continue;
                }
            };
        // println!("Slot: {}", slot_entry.slot);
        for entry in entries {
            let txs = entry.transactions;
            for tx in txs {
                let message = tx.message;
                let account_keys = message.static_account_keys();
                // if !account_keys.contains(&Pubkey::from_str(pump_mint_authority).unwrap()) {
                //     continue;
                // }
                let instructions = message.instructions();
                for instruction in instructions {
                    if account_keys[instruction.program_id_index as usize].to_string() == pump_manager && instruction.data[..8] == [0x18, 0x1e, 0xc8, 0x28, 0x05, 0x1c, 0x07, 0x77] {
                        println!("Signature: {}, length: {}", tx.signatures[0], instruction.data.len());
                        println!("Mint: {}", account_keys[instruction.accounts[0] as usize]);
                        let data = &instruction.data[8..];
                        let config = bincode::DefaultOptions::new()
                            .with_fixint_encoding();
                        match config.deserialize::<MintParams>(&data) {
                            Ok(decoded_args) => {
                                println!("Decoded MintParams: {:?}", decoded_args);
                            }
                            Err(e) => {
                                println!("Failed to decode Args: {e}");
                            }
                        }
                    }
                }
            }
        }
    }
    Ok(())
}
