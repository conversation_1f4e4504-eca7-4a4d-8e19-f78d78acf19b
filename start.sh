#!/bin/bash
rm logs/sig_message.txt
rm logs/buy_trade_history.txt
rm logs/sell_trade_history.txt
rm logs/dev_sell_alert.txt
rm logs/step0_new_token_alert.txt
rm logs/custom_sell_alerts.txt
rm logs/bought_tokens.txt
rm logs/remove_rug_alert.txt
rm logs/skip_token_alert.txt
rm logs/ix_data.txt
rm logs/unknown_trade_history.txt
rm logs/noactivity_trades_history_debug.txt
rm logs/dev_sell_custom_trades_history_debug.txt
rm logs/dev_initial_buy_amount_debug.txt
rm logs/no_trades_history_debug.txt
rm logs/not_bought_tokens_alert.txt
rm logs/twitter_info_debug.txt
rm logs/jup_buy_alert.txt
rm logs/potential_tokens_step1.txt
rm logs/potential_tokens_step2.txt
rm logs/market_cap_message.txt
rm logs/rug_alert.txt
rm logs/potential_tokens_step3.txt
rm logs/potential_tokens_step3_debug1.txt
rm logs/potential_tokens_step3_debug2.txt
rm logs/mint_key_already_monitored.txt
rm logs/stop_loss_history.txt
rm logs/stop_loss_history_5_secs.txt
rm logs/stop_loss_history_4_secs.txt
rm logs/sell_tokens.txt
rm logs/bought_tokens.txt
rm logs/profit_alert.txt
rm logs/profit_progress_alert.txt
rm logs/no_action_taken.txt
rm logs/axiom_twitter_field.txt
rm logs/step1_basic_info_completed.txt

clear
cargo build -p pump-tester --release
./target/release/pump-tester --release