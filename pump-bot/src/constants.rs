use std::env;

pub const PUMP_FUN_PROGRAM_ID: &str = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";
pub const PHOTON_PROGRAM_ID: &str = "BSfD6SHZigAfDWSjzD5Q41jw8LmKwtmjskPH9XW1mrRW";
pub const BLOOM_PROGRAM_ID: &str = "b1oomGGqPKGD6errbyfbVMBuzSC8WtAAYo8MwNafWW1";

pub const CREATE_DISCRIMINATOR: [u8; 8] = [0x18, 0x1e, 0xc8, 0x28, 0x05, 0x1c, 0x07, 0x77];
pub const PUMP_BUY_IX_DISCRIMINATOR: [u8; 8] = [0x52, 0xe1, 0x77, 0xe7, 0x4e, 0x1d, 0x2d, 0x46];
pub const PUMP_BUY_IX_DISCRIMINATOR1: [u8; 8] = [0xfa, 0xea, 0x0d, 0x7b, 0xd5, 0x9c, 0x13, 0xec];
pub const PUMP_SELL_IX_DISCRIMINATOR: [u8; 8] = [0x5d, 0x58, 0x3c, 0x22, 0x5b, 0x12, 0x56, 0xc5];
pub const PUMP_SELL_IX_DISCRIMINATOR1: [u8; 8] = [0x2c, 0x77, 0xaf, 0xda, 0xc7, 0x4d, 0xc4, 0xeb];
pub const PUMP_SELL_IX_DISCRIMINATOR2: [u8; 8] = [0x95, 0x27, 0xde, 0x9b, 0xd3, 0x7c, 0x98, 0x1a];

pub const PUMP_BUY_SELL_MINT_INDEX: usize = 2;
pub const PUMP_BUY_SELL_CURVE_INDEX: usize = 3;
pub const PUMP_BUY_SELL_USER_INDEX: usize = 6;

pub const MINT_TOKEN_INDEX: usize = 0;
pub const MINT_CURVE_INDEX: usize = 2;
pub const MINT_USER_INDEX: usize = 7;

// Environment-configurable API key
pub fn get_sol_track_api_key() -> String {
    env::var("SOL_TRACK_API_KEY").unwrap_or_else(|_| "73155f80-4998-4182-90a0-e53b057ff94d".to_string())
}

// Environment-configurable constants with fallback defaults
pub fn get_shredstream_endpoint() -> String {
    env::var("SHREDSTREAM_ENDPOINT").unwrap_or_else(|_| "http://127.0.0.1:9999".to_string())
}

// Trading threshold configuration
pub fn get_market_cap_threshold() -> f64 {
    env::var("MARKET_CAP_THRESHOLD")
        .unwrap_or_else(|_| "9500.0".to_string())
        .parse()
        .unwrap_or(9500.0)
}

pub fn get_token_age_threshold() -> u64 {
    env::var("TOKEN_AGE_THRESHOLD")
        .unwrap_or_else(|_| "5".to_string())
        .parse()
        .unwrap_or(5)
}

pub fn get_dev_sell_percentage_threshold() -> f64 {
    env::var("DEV_SELL_PERCENTAGE_THRESHOLD")
        .unwrap_or_else(|_| "90.0".to_string())
        .parse()
        .unwrap_or(90.0)
}

// API rate limiting configuration
pub fn get_api_rate_limit_delay() -> u64 {
    env::var("API_RATE_LIMIT_DELAY")
        .unwrap_or_else(|_| "100".to_string())
        .parse()
        .unwrap_or(100)
}

pub fn get_step1_processing_interval() -> u64 {
    env::var("STEP1_PROCESSING_INTERVAL")
        .unwrap_or_else(|_| "500".to_string())
        .parse()
        .unwrap_or(500)
}

pub fn get_step2_processing_interval() -> u64 {
    env::var("STEP2_PROCESSING_INTERVAL")
        .unwrap_or_else(|_| "200".to_string())
        .parse()
        .unwrap_or(200)
}

pub fn get_step3_processing_interval() -> u64 {
    env::var("STEP3_PROCESSING_INTERVAL")
        .unwrap_or_else(|_| "200".to_string())
        .parse()
        .unwrap_or(200)
}

// Optimized discriminator lookup using a hashmap for O(1) lookups
use std::collections::HashMap;
use std::sync::OnceLock;

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TransactionType {
    PumpCreate,
    PumpBuy,
    PumpSell,
}

static DISCRIMINATOR_MAP: OnceLock<HashMap<[u8; 8], TransactionType>> = OnceLock::new();

pub fn get_discriminator_map() -> &'static HashMap<[u8; 8], TransactionType> {
    DISCRIMINATOR_MAP.get_or_init(|| {
        let mut map = HashMap::new();
        map.insert(CREATE_DISCRIMINATOR, TransactionType::PumpCreate);
        map.insert(PUMP_BUY_IX_DISCRIMINATOR, TransactionType::PumpBuy);
        map.insert(PUMP_BUY_IX_DISCRIMINATOR1, TransactionType::PumpBuy);
        map.insert(PUMP_SELL_IX_DISCRIMINATOR, TransactionType::PumpSell);
        map.insert(PUMP_SELL_IX_DISCRIMINATOR1, TransactionType::PumpSell);
        map.insert(PUMP_SELL_IX_DISCRIMINATOR2, TransactionType::PumpSell);
        map
    })
}

/// Fast discriminator lookup - O(1) instead of multiple comparisons
pub fn identify_transaction_type(discriminator: &[u8; 8]) -> Option<TransactionType> {
    get_discriminator_map().get(discriminator).copied()
}

// Constants for fees and swap config
pub const COMPUTE_UNIT_PRICE: u64 = 50_000;      // 0.00005 SOL per 1k units
pub const COMPUTE_UNIT_LIMIT: u32 = 100_000;     // conservative limit
pub const DEFAULT_SWAP_AMOUNT: u64 = 90_000;     // amount to actually swap
pub const PHOTON_FEE_AMOUNT: u64 = 5_000;        // minimum validator tip
pub const SLIPPAGE_BPS: u64 = 500;               // 5% slippage = 500 basis points
// Environment-configurable constants with fallback defaults
// RECENT_WINDOW removed - consolidated into METRICS_WINDOW as per client request

pub fn get_token_cleanup_time() -> u64 {
    env::var("TOKEN_CLEANUP_TIME")
        .unwrap_or_else(|_| "1800".to_string())  // 30 minutes = 1800 seconds
        .parse()
        .unwrap_or(1800)
}

pub fn get_metrics_window() -> u64 {
    env::var("METRICS_WINDOW")
        .unwrap_or_else(|_| "3".to_string())
        .parse()
        .unwrap_or(3)
}

pub fn get_min_instruction_count() -> usize {
    env::var("MIN_INSTRUCTION_COUNT")
        .unwrap_or_else(|_| "2".to_string())
        .parse()
        .unwrap_or(2)
}

pub fn get_min_account_count() -> usize {
    env::var("MIN_ACCOUNT_COUNT")
        .unwrap_or_else(|_| "8".to_string())
        .parse()
        .unwrap_or(8)
}

pub fn get_metrics_output_interval() -> u64 {
    env::var("METRICS_OUTPUT_INTERVAL")
        .unwrap_or_else(|_| "2".to_string())
        .parse()
        .unwrap_or(2)
}

// Legacy constants for backward compatibility
// RECENT_WINDOW removed - consolidated into METRICS_WINDOW as per client request
pub const TOKEN_CLEANUP_TIME: u64 = 1800;        // 1800 seconds = 30 minutes
pub const METRICS_WINDOW: u64 = 3;              // 3 seconds - buy/sell metrics window after creator sold
pub const MIN_INSTRUCTION_COUNT: usize = 2;      // Minimum instructions for likely successful tx
pub const MIN_ACCOUNT_COUNT: usize = 8;          // Minimum accounts for valid pump.fun transaction