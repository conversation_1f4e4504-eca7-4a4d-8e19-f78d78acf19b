use std::env;
use std::fmt;

#[derive(Debug)]
pub struct ConfigError {
    pub message: String,
}

impl fmt::Display for ConfigError {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "Configuration Error: {}", self.message)
    }
}

impl std::error::Error for ConfigError {}

/// Validates all required environment variables are present and have valid values
pub fn validate_configuration() -> Result<(), ConfigError> {
    let mut errors = Vec::new();

    // Required environment variables
    let required_vars = vec![
        "PRIVATE_KEY",
        "HELIUS_RPC_URL",
        "SOL_TRACK_API_KEY",
    ];

    for var in required_vars {
        if env::var(var).is_err() || env::var(var).unwrap().is_empty() {
            errors.push(format!("Missing required environment variable: {}", var));
        }
    }

    // Validate numeric configuration values
    if let Err(_) = crate::constants::get_market_cap_threshold().to_string().parse::<f64>() {
        errors.push("MARKET_CAP_THRESHOLD must be a valid number".to_string());
    }

    if let Err(_) = crate::constants::get_token_age_threshold().to_string().parse::<u64>() {
        errors.push("TOKEN_AGE_THRESHOLD must be a valid number".to_string());
    }

    if let Err(_) = crate::constants::get_dev_sell_percentage_threshold().to_string().parse::<f64>() {
        errors.push("DEV_SELL_PERCENTAGE_THRESHOLD must be a valid number".to_string());
    }

    // Validate API endpoints
    let shredstream_endpoint = crate::constants::get_shredstream_endpoint();
    if !shredstream_endpoint.starts_with("http://") && !shredstream_endpoint.starts_with("https://") {
        errors.push("SHREDSTREAM_ENDPOINT must be a valid HTTP/HTTPS URL".to_string());
    }

    if let Ok(helius_url) = env::var("HELIUS_RPC_URL") {
        if !helius_url.starts_with("http://") && !helius_url.starts_with("https://") {
            errors.push("HELIUS_RPC_URL must be a valid HTTP/HTTPS URL".to_string());
        }
    }

    // Validate private key format (basic check)
    if let Ok(private_key) = env::var("PRIVATE_KEY") {
        if private_key.len() < 32 {
            errors.push("PRIVATE_KEY appears to be too short".to_string());
        }
    }

    // Validate API key format (basic check)
    let api_key = crate::constants::get_sol_track_api_key();
    if api_key.len() < 10 {
        errors.push("SOL_TRACK_API_KEY appears to be invalid".to_string());
    }

    if !errors.is_empty() {
        return Err(ConfigError {
            message: errors.join("; "),
        });
    }

    Ok(())
}

/// Prints current configuration values (without sensitive data)
pub fn print_configuration() {
    println!("🔧 Bot Configuration:");
    println!("  Shredstream Endpoint: {}", crate::constants::get_shredstream_endpoint());
    println!("  Market Cap Threshold: ${}", crate::constants::get_market_cap_threshold());
    println!("  Token Age Threshold: {}s", crate::constants::get_token_age_threshold());
    println!("  Dev Sell Threshold: {}%", crate::constants::get_dev_sell_percentage_threshold());
    println!("  Metrics Window: {}s", crate::constants::get_metrics_window());
    println!("  Token Cleanup Time: {}s", crate::constants::get_token_cleanup_time());
    println!("  API Rate Limit Delay: {}ms", crate::constants::get_api_rate_limit_delay());
    println!("  Step1 Processing Interval: {}ms", crate::constants::get_step1_processing_interval());
    println!("  Step2 Processing Interval: {}ms", crate::constants::get_step2_processing_interval());
    println!("  Step3 Processing Interval: {}ms", crate::constants::get_step3_processing_interval());
    
    // Print environment status without revealing sensitive values
    println!("  Private Key: {}", if env::var("PRIVATE_KEY").is_ok() { "✅ Set" } else { "❌ Missing" });
    println!("  Helius RPC URL: {}", if env::var("HELIUS_RPC_URL").is_ok() { "✅ Set" } else { "❌ Missing" });
    println!("  SOL Track API Key: {}", if !crate::constants::get_sol_track_api_key().is_empty() { "✅ Set" } else { "❌ Missing" });
    println!("  WebSocket URL: {}", if env::var("SOLTRACKER_WSS_URL").is_ok() { "✅ Set" } else { "⚠️  Using default" });
}
