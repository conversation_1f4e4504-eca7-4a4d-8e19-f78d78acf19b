use std::time::Duration;
use tokio::time::sleep;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct RetryConfig {
    pub max_attempts: u32,
    pub base_delay_ms: u64,
    pub max_delay_ms: u64,
    pub backoff_multiplier: f64,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            base_delay_ms: 100,
            max_delay_ms: 5000,
            backoff_multiplier: 2.0,
        }
    }
}

/// Retry a future with exponential backoff
pub async fn retry_with_backoff<F, Fut, T, E>(
    operation: F,
    config: RetryConfig,
    operation_name: &str,
) -> Result<T, E>
where
    F: Fn() -> Fut,
    Fut: std::future::Future<Output = Result<T, E>>,
    E: std::fmt::Display,
{
    let mut attempt = 1;
    let mut delay_ms = config.base_delay_ms;

    loop {
        match operation().await {
            Ok(result) => {
                if attempt > 1 {
                    println!("✅ {} succeeded on attempt {}", operation_name, attempt);
                }
                return Ok(result);
            }
            Err(error) => {
                if attempt >= config.max_attempts {
                    println!("❌ {} failed after {} attempts: {}", operation_name, attempt, error);
                    return Err(error);
                }

                println!("⚠️  {} failed on attempt {} ({}), retrying in {}ms...", 
                    operation_name, attempt, error, delay_ms);

                sleep(Duration::from_millis(delay_ms)).await;

                attempt += 1;
                delay_ms = std::cmp::min(
                    (delay_ms as f64 * config.backoff_multiplier) as u64,
                    config.max_delay_ms,
                );
            }
        }
    }
}

/// Circuit breaker state
#[derive(Debug, Clone)]
pub enum CircuitState {
    Closed,
    Open { opened_at: std::time::Instant },
    HalfOpen,
}

/// Simple circuit breaker implementation
#[derive(Debug)]
pub struct CircuitBreaker {
    state: std::sync::Mutex<CircuitState>,
    failure_count: std::sync::atomic::AtomicU32,
    failure_threshold: u32,
    timeout_duration: Duration,
}

impl CircuitBreaker {
    pub fn new(failure_threshold: u32, timeout_duration: Duration) -> Self {
        Self {
            state: std::sync::Mutex::new(CircuitState::Closed),
            failure_count: std::sync::atomic::AtomicU32::new(0),
            failure_threshold,
            timeout_duration,
        }
    }

    pub async fn call<F, Fut, T, E>(&self, operation: F, operation_name: &str) -> Result<T, CircuitBreakerError<E>>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = Result<T, E>>,
        E: std::fmt::Display,
    {
        // Check if circuit is open
        {
            let mut state = self.state.lock().unwrap();
            match *state {
                CircuitState::Open { opened_at } => {
                    if opened_at.elapsed() > self.timeout_duration {
                        *state = CircuitState::HalfOpen;
                        println!("🔄 Circuit breaker for {} transitioning to half-open", operation_name);
                    } else {
                        return Err(CircuitBreakerError::CircuitOpen);
                    }
                }
                _ => {}
            }
        }

        // Execute operation
        match operation().await {
            Ok(result) => {
                // Reset failure count on success
                self.failure_count.store(0, std::sync::atomic::Ordering::SeqCst);
                
                // Close circuit if it was half-open
                {
                    let mut state = self.state.lock().unwrap();
                    if matches!(*state, CircuitState::HalfOpen) {
                        *state = CircuitState::Closed;
                        println!("✅ Circuit breaker for {} closed after successful call", operation_name);
                    }
                }
                
                Ok(result)
            }
            Err(error) => {
                // Increment failure count
                let failures = self.failure_count.fetch_add(1, std::sync::atomic::Ordering::SeqCst) + 1;
                
                // Open circuit if threshold exceeded
                if failures >= self.failure_threshold {
                    let mut state = self.state.lock().unwrap();
                    *state = CircuitState::Open { opened_at: std::time::Instant::now() };
                    println!("🚨 Circuit breaker for {} opened after {} failures", operation_name, failures);
                }
                
                Err(CircuitBreakerError::OperationFailed(error))
            }
        }
    }
}

#[derive(Debug)]
pub enum CircuitBreakerError<E> {
    CircuitOpen,
    OperationFailed(E),
}

impl<E: std::fmt::Display> std::fmt::Display for CircuitBreakerError<E> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CircuitBreakerError::CircuitOpen => write!(f, "Circuit breaker is open"),
            CircuitBreakerError::OperationFailed(e) => write!(f, "Operation failed: {}", e),
        }
    }
}

impl<E: std::error::Error + 'static> std::error::Error for CircuitBreakerError<E> {
    fn source(&self) -> Option<&(dyn std::error::Error + 'static)> {
        match self {
            CircuitBreakerError::CircuitOpen => None,
            CircuitBreakerError::OperationFailed(e) => Some(e),
        }
    }
}
